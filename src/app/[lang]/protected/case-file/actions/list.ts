"use server";

import { CaseFileService } from "../lib/services";
import { CaseFile, CaseFileListParams } from "../lib/types";
import { ActionState } from "@/lib/types/responses";
import { logger } from "@/lib/logger/services/LoggerService";
import { requirePermission } from "@/lib/authorization/utils/withPermissionDecorator";
import { DOMAIN_PERMISSIONS } from "../lib/config/domain";

/**
 * List case files with filtering, pagination, and sorting
 * @param params Parameters for filtering, pagination, and sorting
 * @returns ActionState with the list of case files and total count
 */
export const listCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  params: CaseFileListParams = {}
): Promise<ActionState<{ items: CaseFile[]; total: number } | null>> => {
  try {
    // Get case files from service
    const response = await CaseFileService.list(params);

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to list case files",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data,
    };
  } catch (error) {
    logger.error(`Unexpected error listing case files: ${error}`);
    return {
      success: false,
      error: `Unexpected error listing case files: ${error}`,
      data: null,
    };
  }
});

/**
 * Search case files by case number or contact name
 * @param query Search query string
 * @param limit Maximum number of results to return
 * @returns ActionState with matching case files
 */
export const searchCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  query: string,
  limit: number = 10
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Validate required fields
    if (!query || query.trim().length === 0) {
      return {
        success: true,
        error: "",
        data: [],
      };
    }

    // Search case files using the list function with search parameter
    const response = await CaseFileService.list({
      search: query.trim(),
      limit,
      page: 1,
      sortBy: "updated_at",
      sortOrder: "desc",
    });

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to search case files",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error searching case files: ${error}`);
    return {
      success: false,
      error: `Unexpected error searching case files: ${error}`,
      data: null,
    };
  }
});

/**
 * Get case files by status
 * @param status The status to filter by
 * @param limit Maximum number of results to return
 * @returns ActionState with case files of the specified status
 */
export const getCaseFilesByStatus = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  status: "opening" | "active" | "suspended" | "closed",
  limit: number = 50
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Get case files by status using the list function
    const response = await CaseFileService.list({
      status,
      limit,
      page: 1,
      sortBy: "updated_at",
      sortOrder: "desc",
    });

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get case files by status",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case files by status: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case files by status: ${error}`,
      data: null,
    };
  }
});

/**
 * Get case files assigned to a specific employee
 * @param employeeId The ID of the employee
 * @param limit Maximum number of results to return
 * @returns ActionState with case files assigned to the employee
 */
export const getCaseFilesByAssignee = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  employeeId: string,
  limit: number = 50
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Validate required fields
    if (!employeeId) {
      return {
        success: false,
        error: "Employee ID is required",
        data: null,
      };
    }

    // Get case files by assignee using the list function
    const response = await CaseFileService.list({
      assigned_to: employeeId,
      limit,
      page: 1,
      sortBy: "updated_at",
      sortOrder: "desc",
    });

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get case files by assignee",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error getting case files by assignee: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting case files by assignee: ${error}`,
      data: null,
    };
  }
});

/**
 * Get recent case files (last 30 days)
 * @param limit Maximum number of results to return
 * @returns ActionState with recent case files
 */
export const getRecentCaseFiles = requirePermission(DOMAIN_PERMISSIONS.VIEW)(async (
  limit: number = 20
): Promise<ActionState<CaseFile[] | null>> => {
  try {
    // Calculate date 30 days ago
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Get recent case files using the list function
    const response = await CaseFileService.list({
      from_date: thirtyDaysAgo.toISOString(),
      limit,
      page: 1,
      sortBy: "created_at",
      sortOrder: "desc",
    });

    if (!response.success || !response.data) {
      return {
        success: false,
        error: response.message || "Failed to get recent case files",
        data: null,
      };
    }

    return {
      success: true,
      error: "",
      data: response.data.items,
    };
  } catch (error) {
    logger.error(`Unexpected error getting recent case files: ${error}`);
    return {
      success: false,
      error: `Unexpected error getting recent case files: ${error}`,
      data: null,
    };
  }
});
