import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { H1, P } from "@/components/typography";
import { Search, Plus } from "lucide-react";
import Link from "next/link";
import { listCaseFiles } from "../actions";
import { CaseFileCard } from "../components";
import { SearchAndFilter } from "../components/SearchAndFilter";

interface CaseFileListPageProps {
  params: Promise<{
    lang: string;
  }>;
  searchParams: Promise<{
    search?: string;
    status?: string;
    page?: string;
  }>;
}

/**
 * Case File List Page
 * Displays all case files with search and filtering capabilities
 */
export default async function CaseFileListPage({ params, searchParams }: CaseFileListPageProps) {
  const { lang } = await params;
  const { search, status, page } = await searchParams;

  // Get case files with current filters
  const caseFilesResponse = await listCaseFiles({
    search: search || undefined,
    status: status as "opening" | "active" | "suspended" | "closed" | undefined,
    page: parseInt(page || "1"),
    limit: 20,
    sortBy: "updated_at",
    sortOrder: "desc",
  });

  const caseFiles =
    caseFilesResponse.success && caseFilesResponse.data
      ? caseFilesResponse.data.items.map((caseFile) => ({
          id: caseFile.id,
          case_number: caseFile.case_number,
          status: caseFile.status,
          created_at: caseFile.created_at,
          activated_at: caseFile.activated_at,
          suspended_at: caseFile.suspended_at,
          closed_at: caseFile.closed_at,
          assigned_to: caseFile.assigned_to,
          primaryContactName: undefined,
          totalContacts: 0,
          upcomingAppointments: 0,
          pendingDocuments: 0,
        }))
      : [];

  const total =
    caseFilesResponse.success && caseFilesResponse.data ? caseFilesResponse.data.total : 0;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <H1>All Case Files</H1>
          <P className="text-muted-foreground mt-1">
            Manage and view all case files in your organization
          </P>
        </div>
        <Button asChild>
          <Link href={`/${lang}/protected/case-file/create`}>
            <Plus className="h-4 w-4 mr-2" />
            New Case File
          </Link>
        </Button>
      </div>

      {/* Search and Filters */}
      <SearchAndFilter lang={lang} />

      {/* Case Files List */}
      <Card>
        <CardHeader>
          <CardTitle>Case Files ({total} total)</CardTitle>
        </CardHeader>
        <CardContent>
          {caseFiles.length > 0 ? (
            <div className="space-y-4">
              {caseFiles.map((caseFile) => (
                <CaseFileCard key={caseFile.id} caseFile={caseFile} lang={lang} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <Search className="h-8 w-8 text-gray-400" />
              </div>
              <H1 className="text-xl font-semibold mb-2">No case files found</H1>
              <P className="text-muted-foreground mb-6">
                {search || status
                  ? "Try adjusting your search criteria or filters"
                  : "Get started by creating your first case file"}
              </P>
              <Button asChild>
                <Link href={`/${lang}/protected/case-file/create`}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Case File
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {total > 20 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <P className="text-sm text-muted-foreground">
                Showing {Math.min(20, total)} of {total} case files
              </P>
              <div className="flex gap-2">
                <Button variant="outline" disabled={!page || parseInt(page) <= 1}>
                  Previous
                </Button>
                <Button variant="outline" disabled={total <= parseInt(page || "1") * 20}>
                  Next
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
