import { Database } from "@/lib/types/database.types";

/**
 * Case File Status type
 * Defines all possible statuses for a case file
 */
export type CaseFileStatus = Database["public"]["Enums"]["case_file_status"];

/**
 * Case File interface
 * Represents a single case file
 * Uses the database Row type for consistency
 */
export type CaseFile = Database["public"]["Tables"]["case_files"]["Row"] & {
  status: CaseFileStatus; // Override status to use our enum type
};

/**
 * Case File Insert interface
 * Used when creating new case files
 */
export type CaseFileInsert = Database["public"]["Tables"]["case_files"]["Insert"] & {
  status?: CaseFileStatus;
};

/**
 * Case File Update interface
 * Used when updating existing case files
 */
export type CaseFileUpdate = Database["public"]["Tables"]["case_files"]["Update"] & {
  status?: CaseFileStatus;
};

/**
 * Case File Contact interface
 * Represents the junction table linking case files to contacts
 */
export type CaseFileContact = Database["public"]["Tables"]["case_file_contacts"]["Row"];

/**
 * Case File Contact Insert interface
 */
export type CaseFileContactInsert = Database["public"]["Tables"]["case_file_contacts"]["Insert"];

/**
 * Case File Contact Update interface
 */
export type CaseFileContactUpdate = Database["public"]["Tables"]["case_file_contacts"]["Update"];

/**
 * Case File History interface
 * Represents audit trail entries for case files
 */
export type CaseFileHistory = Database["public"]["Tables"]["case_file_history"]["Row"];

/**
 * Case File History Insert interface
 */
export type CaseFileHistoryInsert = Database["public"]["Tables"]["case_file_history"]["Insert"];

/**
 * Case File With Relations interface
 * Represents a case file with related data
 */
export interface CaseFileWithRelations extends CaseFile {
  contacts?: Array<{
    id: string;
    name: string;
    email?: any;
    phone?: any;
    relationship_type: string;
  }>;
  request?: {
    id: string;
    reference_number?: string;
    title?: string;
    description?: string;
    service?: {
      id: string;
      name: string;
      description?: string;
    };
  };
  assigned_employee?: {
    id: string;
    first_name?: string;
    last_name?: string;
    email?: string;
  };
  created_by_user?: {
    id: string;
    email: string;
  };
  history?: CaseFileHistory[];
}

/**
 * Case File Dashboard Data interface
 * Aggregated data for the active case file dashboard
 */
export interface CaseFileDashboardData {
  caseFile: CaseFileWithRelations;
  familyInfo: {
    primaryContact?: {
      name: string;
      relationship: string;
      phone?: string;
      email?: string;
    };
    children: Array<{
      name: string;
      age?: number;
      relationship: string;
    }>;
    totalContacts: number;
  };
  serviceRequirements: {
    totalServices: number;
    completedServices: number;
    upcomingAppointments: number;
    pendingDocuments: number;
  };
  recentActivity: Array<{
    id: string;
    type: "appointment" | "document" | "note" | "status_change";
    description: string;
    timestamp: string;
    icon?: string;
  }>;
}

/**
 * Case File List Parameters interface
 * Used for filtering and pagination in case file lists
 */
export interface CaseFileListParams {
  page?: number;
  limit?: number;
  status?: CaseFileStatus | CaseFileStatus[];
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  assigned_to?: string;
  created_by?: string;
  from_date?: string;
  to_date?: string;
}

/**
 * Case File Summary interface
 * Lightweight summary data for case file cards and lists
 */
export interface CaseFileSummary {
  id: string;
  case_number: string;
  status: CaseFileStatus;
  created_at: string;
  activated_at?: string | null;
  suspended_at?: string | null;
  closed_at?: string | null;
  assigned_to?: string | null;
  primaryContactName?: string;
  totalContacts: number;
  upcomingAppointments: number;
  pendingDocuments: number;
}
