import { notFound } from "next/navigation";
import { getCaseFileDashboardData } from "../../actions";
import { CaseFileDashboard } from "../../components";
import { CaseFileHeader } from "../../components/navigation";
import { CaseFileNavigation } from "../../components/navigation";

interface ActiveCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Active Case File Management Page
 * Central hub for managing ongoing family services
 */
export default async function ActiveCaseFilePage({ params }: ActiveCaseFilePageProps) {
  const { lang, id } = await params;

  // Get dashboard data using server action
  const dashboardResponse = await getCaseFileDashboardData(id);

  if (!dashboardResponse.success || !dashboardResponse.data) {
    notFound();
  }

  const { caseFile } = dashboardResponse.data;

  return (
    <div className="space-y-6">
      {/* Case File Header with Breadcrumbs */}
      <CaseFileHeader
        caseFileId={caseFile.id}
        caseNumber={caseFile.case_number}
        status={caseFile.status}
        activatedAt={caseFile.activated_at}
        assignedTo={caseFile.assigned_to}
        lang={lang}
        showBreadcrumbs={true}
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Main Dashboard */}
      <CaseFileDashboard data={dashboardResponse.data} lang={lang} />
    </div>
  );
}
