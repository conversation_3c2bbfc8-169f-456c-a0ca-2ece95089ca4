import { notFound } from "next/navigation";
import { getCaseFileWithRelations } from "../../actions";
import { CaseFileHeader, CaseFileNavigation } from "../../components/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { H2, P } from "@/components/typography";
import { CheckCircle, Clock, FileText, Users } from "lucide-react";

interface OpeningCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Opening Case File Page
 * Displays case files in the opening state with setup checklist
 */
export default async function OpeningCaseFilePage({ params }: OpeningCaseFilePageProps) {
  const { lang, id } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id, true, false);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  // Opening checklist items
  const checklistItems = [
    {
      id: "initial_assessment",
      title: "Initial Assessment Completed",
      description: "Complete initial family assessment and needs evaluation",
      completed: false,
      required: true,
    },
    {
      id: "documentation",
      title: "Required Documentation Collected",
      description: "Gather all necessary documents and forms",
      completed: false,
      required: true,
    },
    {
      id: "service_plan",
      title: "Service Plan Developed",
      description: "Create comprehensive service plan with family",
      completed: false,
      required: true,
    },
    {
      id: "contacts_added",
      title: "Family Contacts Added",
      description: "Add all family members and emergency contacts",
      completed: caseFile.contacts && caseFile.contacts.length > 0,
      required: true,
    },
    {
      id: "case_worker_assigned",
      title: "Case Worker Assigned",
      description: "Assign primary case worker to the family",
      completed: !!caseFile.assigned_to,
      required: true,
    },
  ];

  const completedItems = checklistItems.filter((item) => item.completed).length;
  const totalItems = checklistItems.length;
  const canActivate = checklistItems
    .filter((item) => item.required)
    .every((item) => item.completed);

  return (
    <div className="space-y-6">
      {/* Case File Header */}
      <CaseFileHeader
        caseFileId={caseFile.id}
        caseNumber={caseFile.case_number}
        status={caseFile.status}
        activatedAt={caseFile.activated_at}
        assignedTo={caseFile.assigned_to}
        lang={lang}
        showBreadcrumbs={true}
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Opening Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            Opening Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <div className="flex justify-between items-center mb-2">
              <P className="text-sm font-medium">Setup Progress</P>
              <P className="text-sm text-muted-foreground">
                {completedItems}/{totalItems} completed
              </P>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(completedItems / totalItems) * 100}%` }}
              />
            </div>
          </div>

          {canActivate && (
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <P className="text-sm font-medium text-green-800">
                  Ready to activate! All required items completed.
                </P>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Setup Checklist */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Setup Checklist
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {checklistItems.map((item) => (
              <div key={item.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                <div className="flex-shrink-0 mt-1">
                  {item.completed ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <div className="h-5 w-5 border-2 border-gray-300 rounded-full" />
                  )}
                </div>
                <div className="flex-1">
                  <H2
                    className={`text-base font-medium ${
                      item.completed ? "text-green-800" : "text-gray-900"
                    }`}
                  >
                    {item.title}
                    {item.required && <span className="text-red-500 ml-1">*</span>}
                  </H2>
                  <P className="text-sm text-muted-foreground mt-1">{item.description}</P>
                </div>
                <div className="flex-shrink-0">
                  <Button
                    variant={item.completed ? "outline" : "default"}
                    size="sm"
                    disabled={item.completed}
                  >
                    {item.completed ? "Completed" : "Start"}
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Family Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Family Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          {caseFile.contacts && caseFile.contacts.length > 0 ? (
            <div className="space-y-3">
              {caseFile.contacts.map((contact) => (
                <div
                  key={contact.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <P className="font-medium">{contact.name}</P>
                    <P className="text-sm text-muted-foreground">
                      {contact.email || "No email"} • {contact.phone || "No phone"}
                    </P>
                  </div>
                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <P className="text-sm">No family contacts added yet</P>
              <Button className="mt-3" size="sm">
                Add Family Contact
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <Button variant="outline">Save Progress</Button>
        <Button
          disabled={!canActivate}
          className={canActivate ? "bg-green-600 hover:bg-green-700" : ""}
        >
          Activate Case File
        </Button>
      </div>
    </div>
  );
}
