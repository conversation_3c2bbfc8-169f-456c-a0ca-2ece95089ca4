import { notFound, redirect } from "next/navigation";
import { getCaseFileStatus } from "../actions";

interface DefaultPageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Default case file page that redirects to the current state
 * This page determines the case file's current status and redirects to the appropriate state page
 */
export default async function DefaultCaseFilePage({ params }: DefaultPageProps) {
  const { lang, id } = await params;

  // Get case file status
  const statusResponse = await getCaseFileStatus(id);

  if (!statusResponse.success || !statusResponse.data) {
    notFound();
  }

  const { status } = statusResponse.data;

  // Redirect based on case file status
  switch (status) {
    case "opening":
      redirect(`/${lang}/protected/case-file/${id}/opening`);
    case "active":
      redirect(`/${lang}/protected/case-file/${id}/active`);
    case "suspended":
      redirect(`/${lang}/protected/case-file/${id}/suspended`);
    case "closed":
      redirect(`/${lang}/protected/case-file/${id}/closed`);
    default:
      notFound();
  }
}
