import { notFound } from "next/navigation";
import { getCaseFileWithRelations } from "../../actions";
import { CaseFileHeader, CaseFileNavigation } from "../../components/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { H2, P } from "@/components/typography";
import { CheckCircle, FileText, Calendar, Download, Archive, Users } from "lucide-react";

interface ClosedCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Closed Case File Page
 * Displays completed case information and final reports
 */
export default async function ClosedCaseFilePage({ params }: ClosedCaseFilePageProps) {
  const { lang, id } = await params;

  // Get case file data
  const caseFileResponse = await getCaseFileWithRelations(id, true, false);

  if (!caseFileResponse.success || !caseFileResponse.data) {
    notFound();
  }

  const caseFile = caseFileResponse.data;

  // Mock closure information
  const closureInfo = {
    reason: "Services completed successfully",
    closedDate: caseFile.closed_at || new Date().toISOString(),
    closedBy: "John Doe",
    outcome: "Positive",
    notes:
      "Family has successfully completed all required services. Children are thriving in their current environment. No further intervention required at this time.",
  };

  // Mock final reports and documents
  const finalDocuments = [
    {
      id: "final_report",
      title: "Final Case Report",
      description: "Comprehensive summary of case progression and outcomes",
      type: "report",
      date: closureInfo.closedDate,
      size: "2.4 MB",
    },
    {
      id: "service_summary",
      title: "Service Summary",
      description: "Summary of all services provided during case duration",
      type: "summary",
      date: closureInfo.closedDate,
      size: "1.8 MB",
    },
    {
      id: "family_assessment",
      title: "Final Family Assessment",
      description: "Final assessment of family functioning and wellbeing",
      type: "assessment",
      date: closureInfo.closedDate,
      size: "3.1 MB",
    },
    {
      id: "closure_checklist",
      title: "Closure Checklist",
      description: "Completed checklist verifying all closure requirements met",
      type: "checklist",
      date: closureInfo.closedDate,
      size: "0.5 MB",
    },
  ];

  // Mock case statistics
  const caseStatistics = {
    totalDuration: Math.floor(
      (new Date(closureInfo.closedDate).getTime() - new Date(caseFile.created_at).getTime()) /
        (1000 * 60 * 60 * 24)
    ),
    servicesProvided: 8,
    appointmentsCompleted: 24,
    documentsGenerated: 15,
  };

  return (
    <div className="space-y-6">
      {/* Case File Header */}
      <CaseFileHeader
        caseFileId={caseFile.id}
        caseNumber={caseFile.case_number}
        status={caseFile.status}
        activatedAt={caseFile.activated_at}
        assignedTo={caseFile.assigned_to}
        lang={lang}
        showBreadcrumbs={true}
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Closure Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
            Case Closure Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <H2 className="text-sm font-medium text-muted-foreground">Closure Reason</H2>
                <P className="mt-1">{closureInfo.reason}</P>
              </div>
              <div>
                <H2 className="text-sm font-medium text-muted-foreground">Closed Date</H2>
                <P className="mt-1">{new Date(closureInfo.closedDate).toLocaleDateString()}</P>
              </div>
              <div>
                <H2 className="text-sm font-medium text-muted-foreground">Closed By</H2>
                <P className="mt-1">{closureInfo.closedBy}</P>
              </div>
              <div>
                <H2 className="text-sm font-medium text-muted-foreground">Outcome</H2>
                <Badge
                  className={
                    closureInfo.outcome === "Positive"
                      ? "bg-green-100 text-green-800"
                      : "bg-gray-100 text-gray-800"
                  }
                >
                  {closureInfo.outcome}
                </Badge>
              </div>
            </div>

            {closureInfo.notes && (
              <div>
                <H2 className="text-sm font-medium text-muted-foreground">Closure Notes</H2>
                <P className="mt-1 text-sm bg-gray-50 p-3 rounded-lg">{closureInfo.notes}</P>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Case Statistics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Archive className="h-5 w-5 mr-2" />
            Case Statistics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{caseStatistics.totalDuration}</div>
              <P className="text-sm text-blue-600">Days Active</P>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {caseStatistics.servicesProvided}
              </div>
              <P className="text-sm text-green-600">Services Provided</P>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {caseStatistics.appointmentsCompleted}
              </div>
              <P className="text-sm text-purple-600">Appointments</P>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {caseStatistics.documentsGenerated}
              </div>
              <P className="text-sm text-orange-600">Documents</P>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Final Documents */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Final Documents & Reports
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {finalDocuments.map((document) => (
              <div
                key={document.id}
                className="flex items-center justify-between p-4 border rounded-lg"
              >
                <div className="flex items-center">
                  <div className="bg-blue-100 p-2 rounded-full mr-3">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <H2 className="font-medium">{document.title}</H2>
                    <P className="text-sm text-muted-foreground">{document.description}</P>
                    <P className="text-xs text-muted-foreground mt-1">
                      {new Date(document.date).toLocaleDateString()} • {document.size}
                    </P>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs capitalize">
                    {document.type}
                  </Badge>
                  <Button variant="outline" size="sm">
                    <Download className="h-4 w-4 mr-1" />
                    Download
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Family Information at Closure */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Family Information at Closure
          </CardTitle>
        </CardHeader>
        <CardContent>
          {caseFile.contacts && caseFile.contacts.length > 0 ? (
            <div className="space-y-3">
              {caseFile.contacts.map((contact) => (
                <div
                  key={contact.id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <P className="font-medium">{contact.name}</P>
                    <P className="text-sm text-muted-foreground">
                      {contact.email || "No email"} • {contact.phone || "No phone"}
                    </P>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    Archived
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <P className="text-sm">No family contacts on record</P>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between">
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-1" />
            Download All Documents
          </Button>
          <Button variant="outline">
            <Archive className="h-4 w-4 mr-1" />
            Archive Case
          </Button>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">Print Summary</Button>
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-1" />
            Schedule Follow-up
          </Button>
        </div>
      </div>
    </div>
  );
}
