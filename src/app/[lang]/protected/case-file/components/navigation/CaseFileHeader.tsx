import { H1, Lead, <PERSON> } from "@/components/typography";
import { Badge } from "@/components/ui/badge";
import { FileText, Clock } from "lucide-react";
import { CaseFileStatus } from "../../lib/types";
import { CaseFileBreadcrumbs } from "./CaseFileBreadcrumbs";

interface CaseFileHeaderProps {
  caseFileId: string;
  caseNumber: string;
  status: CaseFileStatus;
  activatedAt?: string | null;
  assignedTo?: string | null;
  lang: string;
  showBreadcrumbs?: boolean;
}

/**
 * Case File Header component
 * Displays case file title, status, and metadata
 */
export function CaseFileHeader({
  caseFileId,
  caseNumber,
  status,
  activatedAt,
  assignedTo,
  lang,
  showBreadcrumbs = true,
}: CaseFileHeaderProps) {
  const getStatusBadge = (status: CaseFileStatus) => {
    switch (status) {
      case "active":
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <Clock className="h-3 w-3 mr-1" />
            Active
          </Badge>
        );
      case "opening":
        return (
          <Badge variant="default" className="bg-orange-100 text-orange-800">
            <Clock className="h-3 w-3 mr-1" />
            Opening
          </Badge>
        );
      case "suspended":
        return (
          <Badge variant="default" className="bg-yellow-100 text-yellow-800">
            <Clock className="h-3 w-3 mr-1" />
            Suspended
          </Badge>
        );
      case "closed":
        return (
          <Badge variant="default" className="bg-gray-100 text-gray-800">
            <Clock className="h-3 w-3 mr-1" />
            Closed
          </Badge>
        );
      default:
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            {status}
          </Badge>
        );
    }
  };

  const formatActivationDate = (activatedAt: string | null | undefined) => {
    if (!activatedAt) return "Active";
    return `Active since ${new Date(activatedAt).toLocaleDateString()}`;
  };

  const formatAssignment = (assignedTo: string | null | undefined) => {
    if (!assignedTo) return "Unassigned";
    return `Assigned to: ${assignedTo}`;
  };

  return (
    <div className="space-y-4">
      {showBreadcrumbs && (
        <CaseFileBreadcrumbs
          caseFileId={caseFileId}
          caseNumber={caseNumber}
          currentStatus={status}
          lang={lang}
        />
      )}

      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-center">
          <div className="bg-primary/10 p-2 rounded-full mr-3">
            <FileText className="h-10 w-10 text-primary" />
          </div>
          <div>
            <H1>Case File {caseNumber}</H1>
            <Lead className="mt-1">
              {status === "active"
                ? formatActivationDate(activatedAt)
                : `Status: ${status.charAt(0).toUpperCase() + status.slice(1)}`}
            </Lead>
          </div>
        </div>

        <div className="flex items-center gap-4">
          {getStatusBadge(status)}
          <P className="text-sm text-muted-foreground">{formatAssignment(assignedTo)}</P>
        </div>
      </div>
    </div>
  );
}
