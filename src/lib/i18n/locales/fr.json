{"common": {"appName": "Si Simple - Plateforme de gestion des visites supervisées", "welcome": "Bienvenue sur la plateforme Si Simple", "loading": "Chargement...", "save": "Enregistrer", "create": "<PERSON><PERSON><PERSON>", "all": "Tous", "language": "<PERSON><PERSON>", "french": "Français", "english": "<PERSON><PERSON><PERSON>", "help": "Aide", "contactUs": "Nous contacter", "error": "<PERSON><PERSON><PERSON>", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "clearFilters": "<PERSON><PERSON><PERSON><PERSON>", "goHome": "Accueil", "pageNotFound": "Page non trouvée", "cancel": "Annuler", "search": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Rechercher...", "clear": "<PERSON><PERSON><PERSON><PERSON>", "actions": "Actions", "saving": "Enregistrement...", "remove": "<PERSON><PERSON><PERSON><PERSON>", "back": "Retour", "goBack": "Retour", "returnToHomepage": "Retour à l'accueil", "errorOccurred": "Une erreur est survenue", "previous": "Précédent", "next": "Suivant", "submit": "So<PERSON><PERSON><PERSON>", "submitting": "Soumission en cours...", "errorMessage": "Une erreur s'est produite. Veuillez réessayer.", "errorId": "ID d'erreur", "profile": "Profil", "account": "<PERSON><PERSON><PERSON>", "signOut": "Déconnexion", "settings": "Paramètres", "notifications": "Notifications", "notSpecified": "Non spécifié", "noAddressProvided": "<PERSON><PERSON>ne adresse fournie", "noEmail": "<PERSON><PERSON><PERSON> co<PERSON>", "noEmailsListed": "<PERSON><PERSON><PERSON> adresse co<PERSON>", "noNumber": "Aucun numéro", "noPhoneNumbers": "Aucun numéro de téléphone listé", "noProfileImage": "Aucune image de profil", "noSpecializationsListed": "Aucune spécialisation listée", "noCertificationsListed": "Aucune certification listée", "noEducationHistory": "Aucun historique d'éducation listé", "noJobTitle": "Aucun titre de poste", "notAssigned": "Non assigné", "noneAssigned": "Aucun <PERSON>", "notLinked": "Non lié", "noPhone": "Pas de numéro de téléphone", "unknown": "Inconnu", "unknownInstitution": "Institution inconnue", "unknownDegree": "<PERSON><PERSON><PERSON><PERSON> inconnu", "unknownField": "Domaine inconnu", "primary": "Principal", "systemInformation": "Informations système", "created": "<PERSON><PERSON><PERSON>", "lastUpdated": "Dernière mise à jour", "notAvailable": "N/D", "unnamed": "Sans nom", "issuer": "<PERSON><PERSON><PERSON>", "obtained": "Obtenu", "expires": "Expire", "present": "Présent", "notFound": {"title": "Page non trouvée", "code": "404", "message": "La page que vous recherchez n'existe pas ou a été déplacée."}, "placeholders": {"enterFirstName": "Entrez le prénom", "enterLastName": "Entrez le nom", "enterFullAddress": "Entrez l'adresse complète", "enterProfileImageUrl": "Entrez l'URL de l'image de profil", "enterEmailAddresses": "Entrez les adresses e-mail (format JSON)", "enterPhoneNumbers": "Entrez les numéros de téléphone (format JSON)", "enterEmployeeId": "Entrez l'ID de l'employé", "selectStatus": "Sélectionnez le statut", "enterJobTitle": "Entrez le titre du poste", "selectDepartment": "Sélectionnez le département", "selectGender": "Sélectionnez le genre", "enterSupervisorId": "Entrez l'ID du superviseur", "enterUserAccountId": "Entrez l'ID du compte utilisateur (optionnel)", "enterSpecializations": "Entrez les spécialisations (séparées par des virgules)", "enterCertifications": "Entrez les certifications (format JSON)", "enterEducation": "Entrez l'historique de formation (format JSON)"}, "gender": {"notSpecified": "Non spécifié", "male": "<PERSON><PERSON>", "female": "<PERSON>mme", "other": "<PERSON><PERSON>", "preferNotToSay": "<PERSON><PERSON><PERSON><PERSON> ne pas préciser"}, "department": {"notSpecified": "Non spécifié", "hr": "<PERSON><PERSON><PERSON><PERSON> humaines", "it": "Technologie de l'information", "finance": "Finance", "operations": "Opérations", "sales": "<PERSON><PERSON><PERSON>", "marketing": "Marketing"}, "edit": "Modifier", "pagination": {"previous": "Précédent", "next": "Suivant", "showing": "Affichage de {count} sur {total} éléments"}, "INSERT": "<PERSON><PERSON><PERSON>", "UPDATE": "<PERSON><PERSON><PERSON><PERSON>", "DELETE": "Supprimé"}, "errors": {"title": "<PERSON><PERSON><PERSON>", "networkError": "<PERSON><PERSON><PERSON> de réseau. Veuillez vérifier votre connexion et réessayer.", "authError": "Erreur d'authentification. Vous devrez peut-être vous reconnecter.", "unexpectedError": "Une erreur inattendue s'est produite. Veuillez réessayer plus tard.", "validationError": "Veuillez vérifier le formulaire pour les erreurs.", "invalidEmail": "Format d'email invalide.", "invalidPhone": "Format de numéro de téléphone invalide.", "invalidLanguage": "Sélection de langue invalide.", "requiredField": "Ce champ est obligatoire.", "minLength": "Ce champ doit comporter au moins {min} caractères.", "maxLength": "Ce champ doit comporter au maximum {max} caractères.", "invalidFormat": "Format invalide.", "duplicateEmail": "Cet email est déjà utilisé par un autre compte."}, "home": {"title": "Si Simple 2025", "description": "Une application moderne avec support du mode sombre", "demoText": "Ceci est une démonstration du système de design avec Tailwind CSS v4 et les composants shadcn/ui.", "themeToggleText": "Essayez de basculer entre les modes clair et sombre en utilisant le bouton dans le coin supérieur droit.", "getStarted": "Commencer"}, "navigation": {"dashboard": "Tableau de bord", "user": "Profil", "contacts": "Contacts", "requests": "<PERSON><PERSON><PERSON>", "cases": "Dossiers", "scheduling": "<PERSON><PERSON><PERSON>", "notes": "Notes", "reports": "Rapports", "settings": "Paramètres", "admin": "Administration", "organizationAdmin": "Administration Systeme", "userManage": "Gestion des accès", "organizationProfile": "Mon organization", "employeeManage": "Gestion des employés", "employeeAvailability": "Gestion des disponibilités"}, "auth": {"signin": {"pageTitle": "Connexion", "pageDescription": "Connectez-vous à votre compte", "title": "Connectez-vous à votre compte", "subtitle": "Entrez vos identifiants pour accéder à votre compte", "emailLabel": "<PERSON><PERSON><PERSON>", "passwordLabel": "Mot de passe", "forgotPassword": "Mot de passe oublié?", "signInButton": "Se connecter", "signingIn": "Connexion en cours...", "noAccount": "Vous n'avez pas de compte?", "contactAdmin": "Contactez votre administrateur", "errorMessage": "Une erreur s'est produite lors de la connexion", "unexpectedError": "Une erreur inattendue s'est produite. Veuillez réessayer."}, "signout": {"title": "Déconnexion", "confirmMessage": "Êtes-vous sûr de vouloir vous déconnecter?", "confirmButton": "<PERSON><PERSON>, me déconnecter", "cancelButton": "Annuler"}, "resetPassword": {"pageTitle": "Réinitialisation du mot de passe", "pageDescription": "Réinitialisez le mot de passe de votre compte", "requestResetTitle": "Réinitialisez votre mot de passe", "setNewPasswordTitle": "Définir un nouveau mot de passe", "emailLabel": "<PERSON><PERSON><PERSON>", "passwordLabel": "Nouveau mot de passe", "confirmPasswordLabel": "Confirmer le nouveau mot de passe", "requestButton": "Envoyer le lien de réinitialisation", "resetButton": "Réinitialiser le mot de passe", "backToSignIn": "Retour à la connexion", "requestSent": "Lien de réinitialisation envoy<PERSON> à votre courriel", "passwordMismatch": "Les mots de passe ne correspondent pas", "resetSuccess": "Mot de passe réinitialisé avec succès"}, "errors": {"notFound": "La page d'authentification que vous recherchez n'existe pas."}}, "user": {"profile": "Profil", "profileDescription": "<PERSON><PERSON><PERSON> vos informations de profil et vos préférences.", "personal": "Personnel", "personalDescription": "Mettez à jour vos informations personnelles.", "account": "<PERSON><PERSON><PERSON>", "accountDescription": "<PERSON><PERSON><PERSON> les informations et le mot de passe de votre compte.", "contact": "Contact", "contactDescription": "Mettez à jour vos informations de contact.", "settings": "Paramètres", "settingsDescription": "<PERSON><PERSON><PERSON> les paramètres et préférences de votre compte.", "firstName": "Prénom", "lastName": "Nom", "email": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "language": "<PERSON><PERSON>", "notifications": "Notifications", "emailNotifications": "Notifications par courriel", "smsNotifications": "Notifications par SMS", "inAppNotifications": "Notifications dans l'application", "profileUpdated": "Profil mis à jour", "personalInfoUpdated": "Informations personnelles mises à jour avec succès.", "contactInfoUpdated": "Informations de contact mises à jour avec succès.", "settingsUpdated": "Paramètres mis à jour avec succès.", "name": "Nom", "role": "R<PERSON><PERSON>", "selectRole": "Sélectionner un rôle", "personalInfo": "Informations personnelles", "contactInfo": "Informations de contact", "systemInfo": "Informations système", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "roles": {"director": "Directeur", "coordinator": "Coordinateur", "socialWorker": "Travailleur social", "systemAdmin": "Administrateur système"}, "management": {"title": "Gestion des utilisateurs", "description": "<PERSON><PERSON>rez les utilisateurs de votre organisation", "userList": "Liste des utilisateurs", "userListDescription": "<PERSON><PERSON>rez les utilisateurs de votre organisation", "createUser": "C<PERSON>er un utilisateur", "createUserDescription": "Ajouter un nouvel utilisateur à votre organisation", "editUser": "Modifier l'utilisateur", "editUserDescription": "Mettre à jour les informations de l'utilisateur", "userDetails": "Détails de l'utilisateur", "userCreated": "Utilisateur c<PERSON>é avec succès", "userUpdated": "Utilisateur mis à jour avec succès", "emailCannotBeChanged": "L'adresse courriel ne peut pas être modifiée", "noUsersFound": "Aucun utilisateur trouvé", "filterByRole": "Filtrer par rôle", "userNotFound": "Utilisateur non trouvé", "userNotFoundDescription": "L'utilisateur que vous recherchez n'existe pas ou vous n'avez pas la permission de le voir."}}, "organization": {"list": "Organisations", "createNew": "Créer une nouvelle organisation", "noOrganizations": "Aucune organisation trouvée. Créez votre première organisation pour commencer.", "details": "Détails de l'organisation", "backToList": "Retour à la liste", "edit": "Modifier", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mise à jour le", "assignDirector": "Assigner un directeur", "profileManagement": "Mon organisation", "profileManagementDescription": "<PERSON><PERSON><PERSON> le profil, les emplacements, les services et les utilisateurs de votre organisation", "generalInfo": "Informations générales", "generalInfoDescription": "Informations de base sur votre organisation", "locations": "Emplacements", "locationsDescription": "<PERSON><PERSON>rez les emplacements et les salles de votre organisation", "services": "Services", "servicesDescription": "Configurez les services offerts par votre organisation", "users": "Utilisateurs", "usersDescription": "<PERSON><PERSON>rez les utilisateurs de votre organisation", "profileUpdated": "Profil de l'organisation mis à jour avec succès", "addLocation": "Ajouter un emplacement", "editLocation": "Modifier l'emplacement", "deleteLocation": "Supprimer l'emplacement", "noLocations": "Aucun emplacement trouvé. Ajoutez votre premier emplacement.", "locationCreated": "Emplacement créé avec succès", "locationUpdated": "Emplacement mis à jour avec succès", "deleteLocationConfirm": "Êtes-vous sûr de vouloir supprimer cet emplacement?", "addService": "Ajouter un service", "editService": "Modifier le service", "deleteService": "Supprimer le service", "noServices": "Aucun service trouvé. Ajoutez votre premier service.", "serviceCreated": "Service créé avec succès", "serviceUpdated": "Service mis à jour avec succès", "deleteServiceConfirm": "Êtes-vous sûr de vouloir supprimer ce service?", "businessHours": "Heures d'ouverture", "businessHoursDescription": "Configurez les heures d'ouverture de votre organisation", "addBusinessHours": "Ajouter des heures d'ouverture", "editBusinessHours": "Modifier les heures d'ouverture", "deleteBusinessHours": "Supprimer les heures d'ouverture", "noBusinessHours": "Aucune heure d'ouverture trouvée. Ajoutez vos premières heures d'ouverture.", "businessHoursCreated": "Heures d'ouverture créées avec succès", "businessHoursUpdated": "Heures d'ouverture mises à jour avec succès", "deleteBusinessHoursConfirm": "Êtes-vous sûr de vouloir supprimer ces heures d'ouverture?", "dayOfWeek": "<PERSON><PERSON> <PERSON>", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "isClosed": "<PERSON><PERSON><PERSON>", "location": "Emplacement", "allLocations": "Tous les emplacements", "description": "Description", "price": "Prix", "duration": "<PERSON><PERSON><PERSON>", "minutes": "minutes", "status": "Statut", "active": "Actif", "inactive": "Inactif", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "rooms": "<PERSON><PERSON>", "roomsDescription": "G<PERSON>rer les salles pour les emplacements de votre organisation", "addRoom": "Ajouter une salle", "editRoom": "Modifier la salle", "deleteRoom": "Supp<PERSON>er la salle", "noRooms": "Aucune salle trouvée. Ajoutez votre première salle.", "roomCreated": "Salle créée avec succès", "roomUpdated": "Salle mise à jour avec succès", "deleteRoomConfirm": "Êtes-vous sûr de vouloir supprimer cette salle?", "addRoomDescription": "Ajouter une nouvelle salle à l'un de vos emplacements", "editRoomDescription": "Modifier les détails et les caractéristiques de la salle", "capacity": "Capacité", "features": "Caractéristiques", "addCustomFeature": "Ajouter une caractéristique personnalisée", "featureName": "Nom de la caractéristique", "featureDescription": "Description de la caractéristique", "add": "Ajouter", "filterByLocation": "Filtrer par emplacement", "form": {"createOrganization": "Créer une organisation", "editOrganization": "Modifier l'organisation", "organizationDetails": "Détails de l'organisation", "contactInfo": "Informations de contact", "name": "Nom de l'organisation", "address": "<PERSON><PERSON><PERSON>", "phone": "Numéro de téléphone", "fax": "Numéro de fax", "email": "<PERSON><PERSON><PERSON>", "supportEmail": "<PERSON><PERSON><PERSON> co<PERSON><PERSON> de <PERSON>", "website": "Site web", "status": "Statut", "active": "Active", "inactive": "Inactive", "suspended": "Su<PERSON>end<PERSON>", "create": "Créer l'organisation", "update": "Mettre à jour l'organisation", "cancel": "Annuler", "required": "Requis", "invalidEmail": "<PERSON><PERSON><PERSON> co<PERSON> invalide", "invalidPhone": "Numéro de téléphone invalide", "firstName": "Prénom", "lastName": "Nom de famille", "assignDirector": "Assigner un directeur", "assignDirectorDescription": "Assigner un directeur à cette organisation", "directorAssigned": "Directeur assigné avec succès!", "assigning": "Assignation en cours..."}, "director": {"assignDirector": "Assigner un directeur", "directorDetails": "Entrez les détails du directeur de l'organisation", "firstName": "Prénom", "lastName": "Nom", "email": "<PERSON><PERSON><PERSON>", "phone": "Numéro de téléphone", "assign": "Assigner le directeur", "cancel": "Annuler"}}, "template-single-crud": {"feature-1": {"title": "Fonctionnalité 1", "description": "Description de la fonctionnalité 1", "createTitle": "<PERSON><PERSON>er un nouvel élément", "createDescription": "Entrez les détails du nouvel élément", "editTitle": "Modifier l'élément", "editDescription": "Mettre à jour les détails de cet élément", "viewTitle": "Détails de l'élément", "removeTitle": "Supprimer l'élément", "removeDescription": "Êtes-vous sûr de vouloir supprimer cet élément?", "removeConfirm": "<PERSON><PERSON>, supprimer", "removeCancel": "Non, annuler", "backToList": "Retour à la liste", "itemCreated": "Élément créé avec succès", "itemUpdated": "Élément mis à jour avec succès", "itemRemoved": "Élément supprimé avec succès", "noItems": "Aucun élément trouvé. Créez votre premier élément pour commencer.", "fields": {"name": "Nom", "description": "Description", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le"}, "status": {"active": "Actif", "inactive": "Inactif", "draft": "Brouillon"}, "actions": {"create": "<PERSON><PERSON><PERSON> un élément", "edit": "Modifier", "view": "Voir", "remove": "<PERSON><PERSON><PERSON><PERSON>"}}}, "automation": {"dashboard": {"title": "Tableau de bord d'automatisation", "employeeCreation": {"title": "Création d'employé", "description": "<PERSON><PERSON>er un nouvel employé", "content": "Utilisez cet assistant pour créer un nouvel employé avec toutes les informations nécessaires.", "startButton": "<PERSON><PERSON><PERSON><PERSON> l'assistant"}, "requestCreation": {"title": "Création de demande", "description": "Créer une nouvelle demande de service", "content": "Utilisez cet assistant pour créer une nouvelle demande de service avec tous les détails nécessaires.", "startButton": "<PERSON><PERSON><PERSON><PERSON> l'assistant"}}, "execution": {"title": "Statut d'exécution du flux de travail", "description": "Suivez l'état de votre exécution de flux de travail"}, "request-wizard": {"title": "<PERSON><PERSON><PERSON> une demande", "description": "Complétez les étapes suivantes pour créer une nouvelle demande", "startButton": "Commencer la création de la demande", "steps": {"basic_info": "Informations de base", "related_contacts": "Contacts associés", "service_requirements": "Exigences de service", "family_availability": "Disponibilité familiale", "review": "Révision"}, "basic_info": {"service": "Service", "selectService": "Sélectionner un service", "location": "Emplacement", "selectLocation": "Sélectionner un emplacement"}, "service_requirements": {"startDate": "Date de début", "selectStartDate": "Sélectionner une date de début", "endDate": "Date de fin", "selectEndDate": "Sélectionner une date de fin", "frequency": "<PERSON><PERSON><PERSON>", "selectFrequency": "Sélectionner une fréquence", "periodicity": "Périodicité", "selectPeriodicity": "Sélectionner une périodicité", "frequencyCount": "<PERSON><PERSON><PERSON>", "enterFrequencyCount": "Entrer le nombre de fois par période", "timesPerPeriod": "fois par période", "daily": "Quotidien", "weekly": "Hebdomadaire", "biweekly": "Bihebdomadaire", "monthly": "<PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON> (minutes)", "enterDuration": "Entrer la durée en minutes", "preferredDays": "Jours préfé<PERSON>s", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "preferredTime": "Moment préféré de la journée", "selectPreferredTime": "Sélectionner le moment préféré", "morning": "<PERSON><PERSON> (8h-12h)", "afternoon": "Après-midi (12h-17h)", "evening": "Soir (17h-21h)", "flexible": "Flexible", "notes": "Notes supplémentaires", "enterNotes": "Entrez toutes exigences ou notes supplémentaires"}, "related_contacts": {"title": "Ajouter des contacts associés", "description": "Sélectionnez les contacts associés à cette demande. <PERSON><PERSON> pouvez ajouter plusieurs contacts avec différentes relations.", "addContact": "Ajouter un contact", "noContacts": "Aucun contact ajouté pour l'instant. Cliquez sur le bouton ci-dessus pour ajouter des contacts.", "selectedContacts": "Contacts sélectionnés", "addNewContact": "Ajouter un nouveau contact", "contact": "Contact", "searchContact": "Rechercher un contact...", "noContactsFound": "Aucun contact trouvé", "minCharactersMessage": "Tapez au moins 2 caractères pour rechercher", "relationship": "Type de relation", "selectRelationship": "Sélectionner un type de relation", "client": "Client", "familyMember": "Membre de la famille", "guardian": "<PERSON><PERSON><PERSON>", "caregiver": "Soignant", "professional": "Professionnel", "other": "<PERSON><PERSON>", "actions": "Actions", "remove": "<PERSON><PERSON><PERSON><PERSON>"}, "review": {"title": "Révision", "description": "Veuillez vérifier les informations ci-dessous avant de soumettre votre demande.", "basicInfo": "Informations de base", "complete": "Complet", "incomplete": "Incomplet", "serviceRequirements": "Exigences de service", "familyAvailability": "Disponibilité familiale", "relatedContacts": "Contacts associés", "submit": "Soumettre la demande", "notProvided": "Non fourni", "notSpecified": "Non spécifié", "noRelatedContacts": "Aucun contact associé a<PERSON>"}, "family_availability": {"title": "Disponibilité familiale", "generalAvailability": "Disponibilité générale", "weekdaysOnly": "Jours de semaine seulement", "weekendsOnly": "Fins de semaine seulement", "bothWeekdaysWeekends": "Jours de semaine et fins de semaine", "flexible": "Flexible", "timeFlexibility": "Flexibilité horaire", "selectTimeFlexibility": "Sélectionner la flexibilité horaire", "veryFlexible": "Très flexible (peut s'adapter à n'importe quel horaire)", "somewhatFlexible": "As<PERSON>z flexible (préfère certains horaires mais peut s'adapter)", "limitedFlexibility": "Flexibilité limitée (nécessite des horaires spécifiques)", "advanceNotice": "<PERSON><PERSON><PERSON><PERSON> requis", "selectAdvanceNotice": "Sélectionner le préavis requis", "hours24": "24 heures", "days2": "2-3 jours", "week1": "1 semaine", "weeks2": "2 semaines", "month1": "1 mois ou plus", "notes": "Notes supplémentaires", "enterNotes": "Entrez toutes notes supplémentaires concernant la disponibilité"}}}, "request": {"title": "<PERSON><PERSON><PERSON>", "description": "Gérer vos demandes", "createNew": "<PERSON><PERSON><PERSON> une nouvelle demande", "noRequests": "Aucune demande trouvée. Créez votre première demande pour commencer.", "details": "<PERSON><PERSON><PERSON> de la demande", "viewTitle": "Voir la demande", "viewDescription": "Voir et gérer les informations de la demande", "backToList": "Retour à la liste", "actions": "Actions", "historyTitle": "Historique de la demande", "historyDescription": "Chronologie des modifications apportées à cette demande", "advancedFilters": "Filtres avancés", "searchRequests": "Rechercher des demandes...", "status": {"draft": "Brouillon", "processing": "En Traitement", "waitlist": "Liste d'attente", "completed": "Complété", "closed": "<PERSON><PERSON><PERSON>"}, "statusDescriptions": {"draft": "État initial, la demande est en cours de préparation et n'a pas encore été soumise", "processing": "La demande a été soumise et est en cours de traitement selon les règles d'affaires", "waitlist": "Le service ne peut pas être fourni immédiatement en raison de contraintes de ressources", "completed": "Le service peut être fourni, le dossier a été initié", "closed": "La demande a été fermée manuellement et n'est plus active"}, "priority": {"low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "Élevée"}, "serviceType": {"supervised_visitation": "Visite supervisée", "exchange": "Échange", "transportation": "Transport", "consultation": "Consultation", "other": "<PERSON><PERSON>"}, "fields": {"title": "Titre", "description": "Description", "status": "Statut", "priority": "Priorité", "serviceType": "Type de service", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "requester": "De<PERSON>eur", "assignee": "<PERSON><PERSON><PERSON>", "startDate": "Date de début", "endDate": "Date de fin", "location": "Emplacement", "service": "Service", "frequency": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "preferredDays": "Jours préfé<PERSON>s", "preferredTime": "<PERSON><PERSON> préfé<PERSON>", "referenceNumber": "Numéro de référence", "contacts": "Contacts", "relatedContacts": "Contacts liés", "actions": "Actions"}, "notSpecified": "Non spécifié", "minutes": "minutes", "serviceTimeline": "Chronologie du service", "requestCreated": "<PERSON><PERSON><PERSON>", "currentStatus": "Statut actuel", "serviceStart": "Début du service", "serviceEnd": "Fin du service", "noContactsAvailable": "Aucun contact disponible", "noLocationSpecified": "Aucun emplacement spécifié", "uniqueIdentifier": "Identifiant unique", "weekdaysOnly": "Jours de semaine seulement", "weekendsOnly": "Fins de semaine seulement", "weekdaysAndWeekends": "Jours de semaine et fins de semaine", "flexible": "Flexible", "periodicity": "Périodicité", "frequencyCount": "<PERSON><PERSON><PERSON>", "timesPerPeriod": "fois par période", "periodicities": {"daily": "Quotidien", "weekly": "Hebdomadaire", "monthly": "<PERSON><PERSON><PERSON>"}, "wizard": {"wizardTitle": "<PERSON><PERSON><PERSON> une nouvelle demande", "wizardDescription": "Complétez les étapes suivantes pour créer une nouvelle demande", "steps": {"basicInfo": "Informations de base", "serviceRequirements": "Exigences de service", "familyAvailability": "Disponibilité familiale", "review": "Révision"}, "stepTitles": {"basicInfo": "Informations de base", "serviceRequirements": "Exigences de service", "familyAvailability": "Disponibilité familiale", "review": "Révision et soumission"}, "buttons": {"nextServiceRequirements": "Suivant: Exigences de service", "nextFamilyAvailability": "Suivant: Disponibilité familiale", "nextReview": "Suivant: Révision", "submit": "Soumettre la demande", "previous": "Précédent", "startOver": "Recommencer"}, "success": {"title": "<PERSON><PERSON><PERSON> c<PERSON> avec succès", "message": "Votre demande a été créée et est maintenant en statut brouillon.", "viewRequest": "Voir la demande", "createAnother": "<PERSON><PERSON><PERSON> une autre demande"}}, "serviceRequirements": {"title": "Exigences de service", "description": "Spécifiez les exigences pour cette demande de service", "frequency": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON> (minutes)", "specialRequirements": "Exigences spéciales", "preferredDays": "Jours préfé<PERSON>s", "preferredTime": "Moment de la journée préféré", "notes": "Notes supplémentaires", "frequencies": {"weekly": "Hebdomadaire", "biweekly": "Bihebdomadaire", "monthly": "<PERSON><PERSON><PERSON>", "oneTime": "Une seule fois"}, "timeOfDay": {"morning": "<PERSON>in", "afternoon": "Après-midi", "evening": "Soir", "noPreference": "Pas de préférence"}, "days": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>"}}, "familyAvailability": {"title": "Disponibilité familiale", "description": "Fournir des informations sur la disponibilité familiale", "generalAvailability": "Disponibilité générale", "specificDates": "Dates spécifiques", "notes": "Notes supplémentaires", "addDate": "Ajouter une date", "removeDate": "<PERSON><PERSON><PERSON><PERSON>", "date": "Date", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "selectContact": "Sélectionnez un contact pour définir sa disponibilité", "changesAutoSaved": "Modifications enregistrées automatiquement", "clickToMark": "Cliquez sur les créneaux horaires pour les marquer comme disponibles", "timeSlots": "créneaux horaires", "noContactsAdded": "Aucun contact ajouté pour l'instant.", "addContacts": "Ajouter des contacts", "selectToSet": "Sélectionnez un contact pour définir sa disponibilité", "weeklyAvailability": "Disponibilité hebdomadaire", "morning": "<PERSON>in", "noon": "Midi", "afternoon": "Après-midi", "evening": "Soir", "available": "Disponible", "noAvailabilitySet": "Aucune disponibilité définie", "availabilitySetFor": "Disponibilité définie pour", "contacts": "contacts", "seeDetails": "Voir les détails dans la grille de disponibilité", "noAvailabilityInfo": "Aucune information de disponibilité fournie"}}, "employee": {"management": {"title": "Gestion des employés", "description": "<PERSON><PERSON>rer les profils et la disponibilité des employés", "createTitle": "<PERSON><PERSON>er un nouvel employé", "createDescription": "Entrez les détails du nouvel employé", "editTitle": "Modifier l'employé", "editDescription": "Mettre à jour les détails de cet employé", "viewTitle": "<PERSON>é<PERSON> de l'employé", "removeTitle": "Supprimer l'employé", "removeDescription": "Êtes-vous sûr de vouloir supprimer cet employé?", "removeConfirm": "<PERSON><PERSON>, supprimer", "removeCancel": "Non, annuler", "removeWarning": "Cette action ne peut pas être annulée. L'employé sera marqué comme licencié.", "backToList": "Retour à la liste", "itemCreated": "Emp<PERSON><PERSON> créé avec succès", "itemUpdated": "Employé mis à jour avec succès", "itemRemoved": "Employé supprimé avec succès", "noItems": "Aucun employé trouvé. Créez votre premier employé pour commencer.", "personalInformation": "Informations personnelles", "tabs": {"contact": "Informations de contact", "employment": "<PERSON><PERSON><PERSON> d'emploi", "professional": "Informations professionnelles", "personal": "Informations personnelles"}, "fields": {"name": "Nom", "description": "Description", "status": "Statut", "firstName": "Prénom", "lastName": "Nom", "fullName": "Nom complet", "profileImage": "Image de profil", "dateOfBirth": "Date de naissance", "gender": "Genre", "address": "<PERSON><PERSON><PERSON>", "employeeId": "ID d'employé", "hireDate": "Date d'embauche", "terminationDate": "Date de fin d'emploi", "employmentStatus": "Statut d'emploi", "jobTitle": "Titre du poste", "department": "Département", "supervisor": "Superviseur", "specializations": "Spécialisations", "certifications": "Certifications", "education": "Éducation", "emails": "<PERSON><PERSON> courriel", "phones": "Numéros de téléphone", "userAccount": "Compte utilisateur", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le"}, "status": {"active": "Actif", "inactive": "Inactif", "draft": "Brouillon"}, "employmentStatus": {"active": "Actif", "inactive": "Inactif", "terminated": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"create": "<PERSON><PERSON><PERSON> un employé", "edit": "Modifier", "view": "Voir", "remove": "<PERSON><PERSON><PERSON><PERSON>", "activate": "Activer", "deactivate": "Désactiver", "linkUserAccount": "Lier un compte utilisateur"}, "confirmDeactivate": "Confirmer la désactivation", "deactivateDescription": "Êtes-vous sûr de vouloir désactiver cet employé? Il n'aura plus accès au système.", "activating": "Activation en cours...", "deactivating": "Désactivation en cours..."}, "wizard": {"title": "Assistant de création d'employé", "description": "<PERSON><PERSON><PERSON> un nouvel employé en utilisant cet assistant étape par étape", "createTitle": "<PERSON><PERSON>er un nouvel employé", "createDescription": "Cet assistant vous guidera à travers le processus de création d'un nouvel employé. Vous pouvez sauvegarder votre progression à tout moment et revenir plus tard pour la compléter.", "startButton": "Commencer la création d'employé", "steps": {"basicInfo": "Informations de base", "contactInfo": "Coordonnées", "employmentDetails": "<PERSON><PERSON><PERSON> d'emploi", "review": "Révision"}, "stepTitles": {"basicInfo": "Informations de base", "contactInfo": "Coordonnées", "employmentDetails": "<PERSON><PERSON><PERSON> d'emploi", "review": "Révision des informations de l'employé"}, "wizardTitle": "<PERSON><PERSON>er un nouvel employé", "wizardDescription": "Complétez les étapes suivantes pour créer un nouvel employé", "fields": {"firstName": "Prénom", "lastName": "Nom", "jobTitle": "Titre du poste", "department": "Département", "address": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "employmentStatus": "Statut d'emploi", "hireDate": "Date d'embauche", "employeeId": "ID d'employé", "supervisorId": "ID du superviseur", "role": "<PERSON><PERSON><PERSON> utilisateur"}, "placeholders": {"enterFirstName": "Entrez le prénom", "enterLastName": "Entrez le nom", "enterJobTitle": "Entrez le titre du poste", "selectDepartment": "Sélectionnez le département", "enterAddress": "Entrez l'adresse", "enterEmail": "Entrez l'adresse courriel", "enterPhone": "Entrez le numéro de téléphone", "selectEmploymentStatus": "Sélectionnez le statut d'emploi", "enterHireDate": "Entrez la date d'embauche", "enterEmployeeId": "Entrez l'ID d'employé", "enterSupervisorId": "Entrez l'ID du superviseur", "selectRole": "Sélectionnez le rôle"}, "buttons": {"previous": "Précédent", "next": "Suivant", "save": "Enregistrer", "submit": "So<PERSON><PERSON><PERSON>", "previousBasicInfo": "Précédent: Informations de base", "previousContactInfo": "Précédent: Coordonnées", "previousEmploymentDetails": "Précédent: <PERSON><PERSON><PERSON>'emploi", "nextContactInfo": "Suivant: Coordonnées", "nextEmploymentDetails": "Suivant: <PERSON><PERSON><PERSON> d'emploi", "nextReview": "Suivant: Révision", "createEmployee": "<PERSON><PERSON>er l'employé"}, "reviewDescription": "Veuillez vérifier les informations ci-dessous avant de soumettre. Vous pouvez revenir à n'importe quelle étape pour apporter des modifications.", "reviewTable": {"field": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, "success": {"title": "Employé créé avec succès!", "description": "L'employé a été créé avec succès. Vous pouvez maintenant voir les détails de l'employé ou créer un autre employé.", "executionId": "ID d'exécution:", "status": "Statut:", "createdAt": "<PERSON><PERSON><PERSON>:", "completed": "Complété", "viewEmployee": "Voir l'employé", "createAnother": "<PERSON><PERSON>er un autre employé"}, "loading": "Chargement...", "roles": {"director": "Directeur", "coordinator": "Coordinateur", "socialWorker": "Travailleur social"}}, "availability": {"title": "Gestion des disponibilités", "description": "Gérer les disponibilités des employés, les demandes de congé et les exceptions", "listTitle": "Disponibilités des employés", "listDescription": "Consulter et gérer les disponibilités des employés", "noItems": "Aucun enregistrement de disponibilité trouvé", "createButton": "Ajouter une disponibilité", "viewTitle": "Détails de disponibilité", "viewDescription": "Consulter les détails de disponibilité d'un employé", "editButton": "Modifier la disponibilité", "backToList": "Retour à la liste", "editTitle": "Modifier la disponibilité", "editDescription": "Mettre à jour la disponibilité d'un employé", "saveButton": "Enregistrer les modifications", "cancelButton": "Annuler", "itemUpdated": "Disponibilité mise à jour avec succès", "createTitle": "Ajouter une disponibilité", "createDescription": "<PERSON><PERSON>er une nouvelle disponibilité pour un employé", "itemCreated": "Disponibilité créée avec succès", "fields": {"employee": "Employé", "dayOfWeek": "<PERSON><PERSON> <PERSON>", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "isRecurring": "<PERSON><PERSON><PERSON>", "recurrencePattern": "<PERSON><PERSON><PERSON><PERSON>", "startDate": "Date de début", "endDate": "Date de fin", "type": "Type", "status": "Statut", "description": "Description", "exceptionDate": "Date d'exception", "isAvailable": "Disponible", "reason": "<PERSON>son"}, "daysOfWeek": {"sunday": "<PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>"}, "timeOffTypes": {"vacation": "Vacances", "sick_leave": "Con<PERSON> maladie", "personal_leave": "Congé personnel", "other": "<PERSON><PERSON>"}, "status": {"pending": "En attente", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>"}, "tabs": {"regularHours": "Heures régulières", "timeOff": "<PERSON><PERSON><PERSON>", "exceptions": "Exceptions"}, "actions": {"approve": "Approuver", "reject": "<PERSON><PERSON><PERSON>", "cancel": "Annuler"}, "featureErrors": {"invalidTimeRange": "Plage horaire invalide. L'heure de fin doit être après l'heure de début.", "invalidDateRange": "Plage de dates invalide. La date de fin doit être après la date de début.", "overlappingAvailability": "Cette disponibilité chevauche une disponibilité existante.", "overlappingTimeOff": "Cette demande de congé chevauche une demande existante."}, "regularHoursTitle": "Disponibilité Hebdomadaire", "regularHoursDescription": "Définir votre disponibilité hebdomadaire régulière", "timeOff": {"title": "<PERSON><PERSON><PERSON>", "createTitle": "<PERSON><PERSON><PERSON> un Con<PERSON>", "editTitle": "Modifier la Demande de Congé", "createDescription": "Soumettre une nouvelle demande de congé", "editDescription": "Modifier votre demande de congé", "listTitle": "<PERSON><PERSON><PERSON>", "noRecords": "Aucune demande de congé trouvée", "addButton": "<PERSON><PERSON><PERSON> un Con<PERSON>", "startDate": "Date de début", "endDate": "Date de fin", "startTime": "Heure de début (optionnel)", "endTime": "Heure de fin (optionnel)", "type": "Type", "selectType": "Sélectionner le type", "description": "Raison/Notes", "createSuccess": "<PERSON><PERSON><PERSON> de congé créée avec succès", "updateSuccess": "<PERSON><PERSON><PERSON> de congé mise à jour avec succès", "deleteSuccess": "<PERSON><PERSON><PERSON> de congé supprimée avec succès", "approveSuccess": "<PERSON><PERSON><PERSON> de congé approuvée", "rejectSuccess": "<PERSON><PERSON><PERSON> de congé rejetée", "cancelSuccess": "<PERSON><PERSON><PERSON> de congé annulée", "deleteConfirmTitle": "Su<PERSON><PERSON>er la Demande de Congé", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer cette demande de congé? Cette action ne peut pas être annulée.", "approveButton": "Approuver", "rejectButton": "<PERSON><PERSON><PERSON>", "cancelButton": "Annuler la Demande", "types": {"vacation": "Vacances", "sickLeave": "Congé Maladie", "personalLeave": "Congé Personnel", "other": "<PERSON><PERSON>"}, "status": {"pending": "En Attente", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>"}}, "exceptions": {"title": "Exceptions de Disponibilité", "createTitle": "<PERSON><PERSON><PERSON> une Exception", "editTitle": "Modifier l'Exception", "createDescription": "<PERSON><PERSON><PERSON> une exception ponctuelle à la disponibilité régulière", "editDescription": "Modifier l'exception de disponibilité", "listTitle": "Exceptions de Disponibilité", "noRecords": "Aucune exception trouvée", "addButton": "Ajouter une Exception", "date": "Date", "reason": "<PERSON>son", "reasonPlaceholder": "Expliquez pourquoi cette exception est nécessaire", "availableLabel": "Disponible", "unavailableLabel": "Indisponible", "createSuccess": "Exception créée avec succès", "updateSuccess": "Exception mise à jour avec succès", "deleteSuccess": "Exception supprimée avec succès", "deleteConfirmTitle": "Supprimer l'Exception", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer cette exception? Cette action ne peut pas être annulée."}}}, "contact": {"management": {"title": "Contacts", "description": "Gérer vos contacts", "createTitle": "<PERSON><PERSON><PERSON> un contact", "createDescription": "Ajouter un nouveau contact à votre organisation", "editTitle": "Modifier le contact", "editDescription": "Mettre à jour les informations du contact", "removeTitle": "Supprimer le contact", "removeDescription": "Êtes-vous sûr de vouloir supprimer ce contact?", "removeConfirm": "<PERSON><PERSON><PERSON><PERSON>", "backToList": "Retour à la liste", "itemUpdated": "Contact mis à jour avec succès", "noItems": "Aucun contact trouvé", "fields": {"name": "Nom", "type": "Type", "status": "Statut", "email": "<PERSON><PERSON><PERSON>", "emailPersonal": "<PERSON><PERSON><PERSON> (Personnel)", "emailWork": "<PERSON><PERSON><PERSON> (Travail)", "phone": "Téléphone", "phoneMobile": "Téléphone (Mobile)", "phoneHome": "Téléphone (Domicile)", "address": "<PERSON><PERSON><PERSON>", "notes": "Notes", "searchPlaceholder": "Rechercher des contacts...", "placeholders": {"name": "Entrez le nom", "emailPersonal": "Entrez le courriel personnel", "emailWork": "Entrez le courriel de travail", "phoneMobile": "Entrez le téléphone mobile", "phoneHome": "Entrez le téléphone domicile", "address": "Entrez l'adresse", "status": "Sélectionnez le statut"}}, "status": {"active": "Actif", "inactive": "Inactif", "archived": "Archivé", "draft": "Brouillon"}, "type": {"family": "<PERSON><PERSON><PERSON>", "professional": "Professionnel", "organization": "Organisation", "other": "<PERSON><PERSON>"}, "actions": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "view": "Voir", "remove": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "removing": "Suppression en cours...", "save": "Enregistrer", "saving": "Enregistrement...", "cancel": "Annuler", "creating": "Création en cours..."}, "messages": {"removeWarning": "Cette action ne peut pas être annulée. Le contact sera marqué comme inactif."}, "history": {"title": "Historique", "description": "Changements récents sur ce contact", "contactHistoryTitle": "Historique du contact", "noChanges": "Aucun changement enregistré", "unknownUser": "Utilisateur inconnu", "recordDeleted": "L'enregistrement a été supprimé", "actions": {"insert": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Supprimé"}}, "relationships": {"title": "Relations", "description": "G<PERSON>rer les relations pour ce contact", "createTitle": "<PERSON><PERSON><PERSON> une relation", "createDescription": "Ajouter une nouvelle relation pour ce contact", "editTitle": "Modifier la relation", "editDescription": "Mettre à jour les informations de relation", "removeTitle": "Supprimer la relation", "removeDescription": "Êtes-vous sûr de vouloir supprimer cette relation?", "removeConfirm": "<PERSON><PERSON><PERSON><PERSON>", "backToContact": "Retour au contact", "noRelationships": "Aucune relation trouvée", "relationshipCreated": "Relation créée avec succès", "relationshipUpdated": "Relation mise à jour avec succès", "relationshipRemoved": "Relation supprimée avec succès", "unknownContact": "Contact inconnu", "fields": {"relatedContact": "Contact lié", "relationship": "Type de relation", "selectContact": "Sélectionner un contact", "selectRelationship": "Sélectionner un type de relation", "searchContacts": "Rechercher des contacts...", "noContactsFound": "Aucun contact trouvé", "minCharactersMessage": "Tapez au moins 2 caractères pour rechercher", "pleaseSelectContact": "Veuillez sélectionner un contact"}, "categories": {"family": "<PERSON><PERSON><PERSON>", "professional": "Professionnel", "other": "<PERSON><PERSON>"}, "types": {"parent": "Parent", "child": "<PERSON><PERSON>", "sibling": "<PERSON><PERSON>/Sœur", "spouse": "Conjoint(e)", "grandparent": "Grand-parent", "grandchild": "<PERSON><PERSON><PERSON><PERSON>", "aunt_uncle": "Tante/Oncle", "niece_nephew": "Nièce/Neveu", "cousin": "Cousin(e)", "step_parent": "Beau-parent", "step_child": "Beau-fils/Belle-fille", "guardian": "<PERSON><PERSON><PERSON>", "ward": "<PERSON><PERSON><PERSON>", "lawyer": "Avocat", "client": "Client", "social_worker": "Travailleur social", "case_manager": "Gestionnaire de cas", "doctor": "Médecin", "patient": "Patient", "therapist": "Thérapeute", "friend": "Ami(e)", "neighbor": "Voisin(e)", "colleague": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}}}}}